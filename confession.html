<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💕 你喜欢我吗？ 💕</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive;
            background: linear-gradient(45deg, #ff9a9e, #fecfef, #fecfef, #ff9a9e);
            background-size: 400% 400%;
            animation: gradientShift 3s ease infinite;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
            transition: all 0.3s ease;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            text-align: center;
            padding: 20px;
            max-width: 90vw;
            transition: all 0.5s ease;
        }

        .title {
            font-size: clamp(2rem, 8vw, 4rem);
            color: #ff1744;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .cute-images {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
            max-width: 100%;
        }

        .cute-img {
            width: clamp(60px, 15vw, 100px);
            height: clamp(60px, 15vw, 100px);
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #ff69b4;
            animation: float 3s ease-in-out infinite;
            transition: all 0.3s ease;
        }

        .cute-img:nth-child(odd) {
            animation-delay: -1s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        .question {
            font-size: clamp(1.5rem, 6vw, 2.5rem);
            color: #e91e63;
            margin: 30px 0;
            font-weight: bold;
        }

        .buttons-container {
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            margin: 40px 0;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: clamp(1rem, 4vw, 1.5rem);
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            font-family: inherit;
        }

        .yes-btn {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            transform: scale(1);
        }

        .yes-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .no-btn {
            background: linear-gradient(45deg, #74b9ff, #a29bfe);
            color: white;
            transform: scale(1);
            position: relative;
        }

        .no-btn:hover {
            transform: scale(0.9);
        }

        .hearts {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        }

        .heart {
            position: absolute;
            font-size: 20px;
            color: #ff1744;
            animation: fall 3s linear infinite;
        }

        @keyframes fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        .shake {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .invert {
            filter: invert(1);
        }

        .collapse {
            animation: collapse 1s ease-in-out;
        }

        @keyframes collapse {
            0% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(0.5) rotate(180deg); }
            100% { transform: scale(0) rotate(360deg); opacity: 0; }
        }

        .fly-away {
            animation: flyAway 2s ease-in-out forwards;
        }

        @keyframes flyAway {
            0% { 
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
            100% { 
                transform: scale(0) rotate(720deg) translateX(200vw);
                opacity: 0;
            }
        }

        .success-message {
            display: none;
            font-size: clamp(1.5rem, 6vw, 3rem);
            color: #ff1744;
            margin-top: 30px;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .sparkle {
            position: absolute;
            width: 10px;
            height: 10px;
            background: radial-gradient(circle, #ffd700, #ffed4e);
            border-radius: 50%;
            animation: sparkle 1s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% {
                transform: scale(0) rotate(0deg);
                opacity: 0;
            }
            50% {
                transform: scale(1) rotate(180deg);
                opacity: 1;
            }
        }

        .glitch {
            animation: glitch 0.3s ease-in-out;
        }

        @keyframes glitch {
            0% { transform: translate(0); }
            20% { transform: translate(-2px, 2px); }
            40% { transform: translate(-2px, -2px); }
            60% { transform: translate(2px, 2px); }
            80% { transform: translate(2px, -2px); }
            100% { transform: translate(0); }
        }

        .rainbow-text {
            background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: rainbow 2s ease infinite;
        }

        @keyframes rainbow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @media (max-width: 768px) {
            .buttons-container {
                flex-direction: column;
                gap: 15px;
            }

            .btn {
                min-width: 200px;
            }

            .cute-images {
                gap: 10px;
            }

            .cute-img {
                width: clamp(50px, 12vw, 80px);
                height: clamp(50px, 12vw, 80px);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">💕 小可爱 💕</h1>
        
        <div class="cute-images">
            <img src="008xLqLsly1hwj9cte0rrg306o06oq3d.jfif" alt="可爱" class="cute-img">
            <img src="008xLqLsly1hwj9ctesm9g306o06oaap.jfif" alt="可爱" class="cute-img">
            <img src="008xLqLsly1hwj9cthyw1g306o06on2d.jfif" alt="可爱" class="cute-img">
            <img src="008xLqLsly1hwj9cti1tqg306o06o10j.jfif" alt="可爱" class="cute-img">
            <img src="starteyes.jfif" alt="可爱" class="cute-img">
        </div>

        <p class="question">你喜欢我吗？ 🥺</p>

        <div class="buttons-container">
            <button class="btn yes-btn" id="yesBtn">喜欢 💖</button>
            <button class="btn no-btn" id="noBtn">不喜欢 😢</button>
        </div>

        <div class="success-message" id="successMessage">
            🎉 我就知道你喜欢我！🎉<br>
            💕 我也超级喜欢你！💕
        </div>
    </div>

    <div class="hearts" id="hearts"></div>

    <script>
        let noClickCount = 0;
        let yesBtn = document.getElementById('yesBtn');
        let noBtn = document.getElementById('noBtn');
        let body = document.body;
        let container = document.querySelector('.container');
        let successMessage = document.getElementById('successMessage');

        // 创建爱心雨效果
        function createHeart() {
            const heart = document.createElement('div');
            heart.className = 'heart';
            heart.innerHTML = '💖';
            heart.style.left = Math.random() * 100 + 'vw';
            heart.style.animationDuration = (Math.random() * 3 + 2) + 's';
            document.getElementById('hearts').appendChild(heart);

            setTimeout(() => {
                heart.remove();
            }, 5000);
        }

        // 震动效果
        function vibrate() {
            if (navigator.vibrate) {
                navigator.vibrate([100, 50, 100]);
            }
        }

        // 创建闪烁星星效果
        function createSparkle(x, y) {
            const sparkle = document.createElement('div');
            sparkle.className = 'sparkle';
            sparkle.style.left = x + 'px';
            sparkle.style.top = y + 'px';
            sparkle.style.position = 'fixed';
            sparkle.style.pointerEvents = 'none';
            sparkle.style.zIndex = '1001';
            document.body.appendChild(sparkle);

            setTimeout(() => {
                sparkle.remove();
            }, 1000);
        }

        // 鼠标移动时创建星星
        document.addEventListener('mousemove', function(e) {
            if (Math.random() < 0.1) { // 10% 概率创建星星
                createSparkle(e.clientX, e.clientY);
            }
        });

        // 触摸移动时创建星星（移动端）
        document.addEventListener('touchmove', function(e) {
            if (Math.random() < 0.2) { // 20% 概率创建星星
                const touch = e.touches[0];
                createSparkle(touch.clientX, touch.clientY);
            }
        });

        // Yes按钮点击事件
        yesBtn.addEventListener('click', function() {
            // 显示成功消息
            successMessage.style.display = 'block';
            
            // 隐藏按钮
            document.querySelector('.buttons-container').style.display = 'none';
            
            // 创建大量爱心
            for (let i = 0; i < 20; i++) {
                setTimeout(createHeart, i * 100);
            }
            
            // 震动
            vibrate();
            
            // 改变背景
            body.style.background = 'linear-gradient(45deg, #ff9a9e, #fad0c4, #ffd1ff, #ff9a9e)';
        });

        // 添加更多恶作剧文本
        const prankTexts = [
            '你确定吗？🤔',
            '再想想？🥺',
            '真的不喜欢吗？😢',
            '我这么可爱你都不喜欢？😭',
            '最后一次机会哦！💔',
            '好吧，我知道你其实喜欢我的！😏'
        ];

        // No按钮点击事件
        noBtn.addEventListener('click', function() {
            noClickCount++;

            // 震动
            vibrate();

            // 更新问题文本
            if (noClickCount <= prankTexts.length) {
                document.querySelector('.question').innerHTML = prankTexts[noClickCount - 1];
            }

            // 各种恶作剧效果
            switch(noClickCount) {
                case 1:
                    // Yes按钮变大，No按钮变小
                    yesBtn.style.transform = 'scale(1.3)';
                    noBtn.style.transform = 'scale(0.8)';
                    body.classList.add('shake');
                    setTimeout(() => body.classList.remove('shake'), 500);
                    // No按钮开始移动
                    noBtn.style.position = 'relative';
                    noBtn.style.left = Math.random() * 20 - 10 + 'px';
                    noBtn.style.top = Math.random() * 20 - 10 + 'px';
                    break;

                case 2:
                    // 继续变化
                    yesBtn.style.transform = 'scale(1.6)';
                    noBtn.style.transform = 'scale(0.6)';
                    // 颜色反转
                    body.classList.add('invert');
                    setTimeout(() => body.classList.remove('invert'), 1000);
                    // No按钮更大幅度移动
                    noBtn.style.left = Math.random() * 40 - 20 + 'px';
                    noBtn.style.top = Math.random() * 40 - 20 + 'px';
                    break;

                case 3:
                    // 更大变化
                    yesBtn.style.transform = 'scale(2)';
                    noBtn.style.transform = 'scale(0.4)';
                    // 页面旋转
                    container.style.transform = 'rotate(10deg)';
                    setTimeout(() => container.style.transform = 'rotate(0deg)', 1000);
                    // No按钮随机跳跃
                    noBtn.style.left = Math.random() * 60 - 30 + 'px';
                    noBtn.style.top = Math.random() * 60 - 30 + 'px';
                    break;

                case 4:
                    // 极端变化
                    yesBtn.style.transform = 'scale(2.5)';
                    noBtn.style.transform = 'scale(0.2)';
                    // 元素崩塌效果
                    document.querySelectorAll('.cute-img').forEach((img, index) => {
                        setTimeout(() => {
                            img.classList.add('collapse');
                            setTimeout(() => img.classList.remove('collapse'), 1000);
                        }, index * 200);
                    });
                    // No按钮疯狂移动
                    noBtn.style.left = Math.random() * 100 - 50 + 'px';
                    noBtn.style.top = Math.random() * 100 - 50 + 'px';
                    break;

                case 5:
                    // 倒数第二次点击
                    yesBtn.style.transform = 'scale(3)';
                    noBtn.style.transform = 'scale(0.1)';
                    // 屏幕闪烁
                    body.style.animation = 'none';
                    body.style.background = '#000';
                    setTimeout(() => {
                        body.style.background = 'linear-gradient(45deg, #ff9a9e, #fecfef, #fecfef, #ff9a9e)';
                        body.style.animation = 'gradientShift 3s ease infinite';
                    }, 200);
                    break;

                default:
                    // 最终效果：No按钮飞出屏幕
                    noBtn.classList.add('fly-away');
                    yesBtn.style.transform = 'scale(3.5)';

                    // 创建爱心雨
                    for (let i = 0; i < 15; i++) {
                        setTimeout(createHeart, i * 100);
                    }

                    setTimeout(() => {
                        noBtn.style.display = 'none';
                        document.querySelector('.question').innerHTML = '既然你点了这么多次，说明你其实很在意我对吧？🥰<br><small>快点击"喜欢"按钮吧！</small>';
                        // 让Yes按钮闪烁
                        yesBtn.style.animation = 'pulse 0.5s infinite';
                    }, 2000);
                    break;
            }
        });

        // No按钮鼠标悬停时逃跑
        noBtn.addEventListener('mouseenter', function() {
            if (noClickCount >= 2) {
                const maxX = window.innerWidth - noBtn.offsetWidth - 20;
                const maxY = window.innerHeight - noBtn.offsetHeight - 20;
                const newX = Math.random() * maxX;
                const newY = Math.random() * maxY;

                noBtn.style.position = 'fixed';
                noBtn.style.left = newX + 'px';
                noBtn.style.top = newY + 'px';
                noBtn.style.transition = 'all 0.3s ease';

                // 添加故障效果
                noBtn.classList.add('glitch');
                setTimeout(() => noBtn.classList.remove('glitch'), 300);
            }
        });

        // 触摸设备上的逃跑效果
        noBtn.addEventListener('touchstart', function(e) {
            if (noClickCount >= 2) {
                e.preventDefault();
                const maxX = window.innerWidth - noBtn.offsetWidth - 20;
                const maxY = window.innerHeight - noBtn.offsetHeight - 20;
                const newX = Math.random() * maxX;
                const newY = Math.random() * maxY;

                noBtn.style.position = 'fixed';
                noBtn.style.left = newX + 'px';
                noBtn.style.top = newY + 'px';

                // 延迟触发点击事件
                setTimeout(() => {
                    noBtn.click();
                }, 100);
            }
        });

        // 随机创建爱心
        setInterval(createHeart, 2000);

        // 页面加载完成后的欢迎效果
        window.addEventListener('load', function() {
            // 创建欢迎爱心
            for (let i = 0; i < 5; i++) {
                setTimeout(createHeart, i * 300);
            }

            // 让标题变成彩虹色
            setTimeout(() => {
                document.querySelector('.title').classList.add('rainbow-text');
            }, 1000);
        });
    </script>
</body>
</html>
